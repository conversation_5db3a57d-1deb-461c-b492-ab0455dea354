import ColumnRender from "@/components/ColumnRender";
import type { ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import type { TransferDetailPostEntity } from '../types/transfer.detail.post.entity';

export interface TransferDetailPostListTableColumnsProps { }

export const TransferDetailPostListTableColumns = (
  props: TransferDetailPostListTableColumnsProps = {},
): ProColumns<TransferDetailPostEntity>[] => {
  const intl = useIntl();
  return [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      editable: false,
      fixed: 'left',
      width: 40,
    },
    {
      title: intl.formatMessage({ id: 'stocks.transfer.detail.columns.itemCode' }),
      dataIndex: 'itemSn',
      key: 'itemSn',
      search: false,
      editable: false,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'stocks.transfer.detail.columns.itemName' }),
      dataIndex: 'itemName',
      key: 'itemName',
      search: false,
      editable: false,
      width: 120,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'stocks.transfer.detail.columns.brandPartNo' }),
      dataIndex: 'brandPartNo',
      key: 'brandPartNo',
      search: false,
      editable: false,
      renderText: (text: string) => ColumnRender.ArrayColumnRender(text?.split(',')),
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'stocks.transfer.detail.columns.brand' }),
      dataIndex: 'brandName',
      key: 'brandName',
      search: false,
      editable: false,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'stocks.transfer.detail.columns.category' }),
      dataIndex: 'categoryName',
      key: 'categoryName',
      search: false,
      editable: false,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'stocks.transfer.detail.columns.unit' }),
      dataIndex: 'unitName',
      key: 'unitName',
      search: false,
      editable: false,
      width: 50,
    },
    /* {
       title: '调出仓库存',
       dataIndex: 'inventoryNum',
       key: 'inventoryNum',
       search: false,
       editable: false,
       hideInTable:true,
       fixed: 'right',
       width: 100,
     },*/
    {
      title: intl.formatMessage({ id: 'stocks.transfer.detail.columns.transferQuantity' }),
      dataIndex: 'transferNum',
      key: 'transferNum',
      search: false,
      editable: false,
      width: 80,
    },
  ] as ProColumns<TransferDetailPostEntity>[];
};
