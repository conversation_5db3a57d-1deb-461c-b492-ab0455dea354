import FunProTable from '@/components/common/FunProTable';
import { PageContainer } from '@ant-design/pro-components';
import type { ActionType } from '@ant-design/pro-table/lib';
import { history, useIntl } from '@umijs/max';
import { useRef, useState } from 'react';
import { PostListTableColumns } from './config/postListTableColumns';

import AuthButton from '@/components/common/AuthButton';
import { addPurchaseReplenish, queryPostList } from '@/pages/purchase/stockUp/list/services';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { useSetState } from 'ahooks';
import { Space } from 'antd';
import { useActivate } from 'react-activation';
import StockByInventoryModal from './components/StockByInventoryModal';
import type { PostEntity } from './types/post.entity';

const StockUpList = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useSetState<any>({
    inventory: false,
  });

  useActivate(() => {
    actionRef.current?.reload();
  });

  const handleAgainItem = async (suggestionNo: string) => {
    history.push(`/purchase/transferSuggestion/detail?suggestionNo=${suggestionNo}`);
  };

  const menuProps = {
    items: [
      {
        label: intl.formatMessage({ id: 'purchase.stockUp.list.menu.stockByInventory' }),
        key: 'inventory',
      },
    ],
    onClick: ({ key }: { key: string }) => {
      setVisible({
        [key]: true,
      });
    },
  };

  const handleInventoryModalOk = (values: any) => {
    console.log('按库存补货表单数据:', values);
    setLoading(true);
    addPurchaseReplenish({
      ruleType: 'BY_STOCK',
      stockCmd: values,
      suggestionType: '2',
    })
      .then((res) => {
        if (res) {
          actionRef.current?.reload();
          setVisible({
            inventory: false,
          });
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <PageContainer>
      <FunProTable<PostEntity, any>
        rowKey="suggestionNo"
        search={{ labelWidth: 100, defaultCollapsed: false }}
        scroll={{ x: 'max-content' }}
        actionRef={actionRef}
        params={{
          suggestionType: '2',
        }}
        // @ts-ignore
        requestPage={queryPostList}
        columns={PostListTableColumns({
          intl,
          handleAgainItem,
        })}
        headerTitle={
          <AuthButton type="primary" authority="" loading={loading} onClick={() => {
            setVisible({
              'inventory': true,
            });
          }}>
            <Space>
              {intl.formatMessage({ id: 'purchase.stockUp.list.button.transferSuggestion' })}
            </Space>
          </AuthButton>
        }
      />
      <StockByInventoryModal
        open={visible.inventory}
        onCancel={() => setVisible({ inventory: false })}
        onOk={handleInventoryModalOk}
        loading={loading}
      />
    </PageContainer>
  );
};
export default withKeepAlive(StockUpList);
